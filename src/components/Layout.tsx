import { Link, Outlet, useLocation } from "react-router-dom";
import { cn } from "@/lib/utils";
import {
  Settings,
  Table,
  Users,
  Code,
  TerminalSquare,
  Database,
  LayoutDashboardIcon,
} from "lucide-react";

const navItems = [
  { href: "/", label: "Dashboard", icon: LayoutDashboardIcon },
  { href: "/configuration", label: "Configuration", icon: Settings },
  { href: "/tables", label: "Tables", icon: Table },
  { href: "/teams", label: "Teams", icon: Users },
  { href: "/sql-editor", label: "SQL Editor", icon: Code },
  { href: "/edge-functions", label: "Edge Functions", icon: TerminalSquare },
];

const Layout = () => {
  const location = useLocation();

  return (
    <div className="flex flex-col min-h-screen bg-gray-50">
      <header className="bg-white border-b border-gray-200 sticky top-0 z-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Link
                to="/"
                className="flex items-center space-x-2 text-xl font-semibold text-gray-800"
              >
                <Database className="h-6 w-6 text-blue-600" />
                <span>VibeCodeBase</span>
              </Link>
            </div>
            <nav className="hidden md:flex space-x-1">
              {navItems.map((item) => (
                <Link
                  key={item.label}
                  to={item.href}
                  className={cn(
                    "px-3 py-2 rounded-md text-sm font-medium transition-colors",
                    (
                      item.href === "/"
                        ? location.pathname === "/"
                        : location.pathname.startsWith(item.href)
                    )
                      ? "bg-blue-50 text-blue-700"
                      : "text-gray-600 hover:bg-gray-100 hover:text-gray-900"
                  )}
                >
                  <item.icon className="inline-block h-4 w-4 mr-1.5 mb-0.5" />
                  {item.label}
                </Link>
              ))}
            </nav>
            {/* Mobile menu button can be added here if needed */}
          </div>
        </div>
      </header>
      <main className="flex-grow container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Outlet />
      </main>
      <footer className="bg-white border-t border-gray-200 py-4 text-center text-sm text-gray-500">
        © {new Date().getFullYear()} My Tech Passport. All rights reserved.
      </footer>
    </div>
  );
};

export default Layout;
